package middleware

import (
	"encoding/json"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/models"
)

const SessionName = "mealpal-session"

func AuthRequired(store sessions.Store) gin.HandlerFunc {
	return func(c *gin.Context) {
		session, err := store.Get(c.Request, SessionName)
		if err != nil {
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		sessionDataJSON, ok := session.Values["session_data"].(string)
		if !ok || sessionDataJSON == "" {
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		var sessionData models.SessionData
		if err := json.Unmarshal([]byte(sessionDataJSON), &sessionData); err != nil {
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		if !sessionData.IsLoggedIn || sessionData.User == nil {
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		// Store user and token in context for handlers to use
		c.Set("user", sessionData.User)
		c.Set("oauth_token", sessionData.OAuthToken)
		c.Next()
	}
}

func GetUserFromContext(c *gin.Context) (*models.User, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}
	
	userModel, ok := user.(*models.User)
	return userModel, ok
}
