package config

import (
	"os"
	"testing"
)

func TestGetEnv(t *testing.T) {
	tests := []struct {
		name         string
		key          string
		defaultValue string
		envValue     string
		expected     string
	}{
		{
			name:         "returns environment variable when set",
			key:          "TEST_KEY",
			defaultValue: "default",
			envValue:     "env_value",
			expected:     "env_value",
		},
		{
			name:         "returns default when environment variable not set",
			key:          "NONEXISTENT_KEY",
			defaultValue: "default",
			envValue:     "",
			expected:     "default",
		},
		{
			name:         "returns empty string when env is empty",
			key:          "EMPTY_KEY",
			defaultValue: "default",
			envValue:     "",
			expected:     "default",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean up environment
			os.Unsetenv(tt.key)
			
			// Set environment variable if provided
			if tt.envValue != "" {
				os.Setenv(tt.key, tt.envValue)
				defer os.Unsetenv(tt.key)
			}

			result := getEnv(tt.key, tt.defaultValue)
			if result != tt.expected {
				t.Errorf("getEnv() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestLoad(t *testing.T) {
	// Save original environment
	originalEnv := map[string]string{
		"PORT":                 os.Getenv("PORT"),
		"SESSION_SECRET":       os.Getenv("SESSION_SECRET"),
		"GOOGLE_CLIENT_ID":     os.Getenv("GOOGLE_CLIENT_ID"),
		"GOOGLE_CLIENT_SECRET": os.Getenv("GOOGLE_CLIENT_SECRET"),
		"GOOGLE_REDIRECT_URL":  os.Getenv("GOOGLE_REDIRECT_URL"),
		"GEMINI_API_KEY":       os.Getenv("GEMINI_API_KEY"),
		"ENVIRONMENT":          os.Getenv("ENVIRONMENT"),
	}

	// Clean up environment
	for key := range originalEnv {
		os.Unsetenv(key)
	}

	// Restore environment after test
	defer func() {
		for key, value := range originalEnv {
			if value != "" {
				os.Setenv(key, value)
			} else {
				os.Unsetenv(key)
			}
		}
	}()

	t.Run("loads config with defaults when no env vars set", func(t *testing.T) {
		// Set required environment variables
		os.Setenv("GOOGLE_CLIENT_ID", "test_client_id")
		os.Setenv("GOOGLE_CLIENT_SECRET", "test_client_secret")
		os.Setenv("GEMINI_API_KEY", "test_gemini_key")

		config := Load()

		if config.Port != "8080" {
			t.Errorf("Expected Port to be '8080', got '%s'", config.Port)
		}
		if config.SessionSecret != "your-secret-key-change-this" {
			t.Errorf("Expected SessionSecret to be default, got '%s'", config.SessionSecret)
		}
		if config.GoogleClientID != "test_client_id" {
			t.Errorf("Expected GoogleClientID to be 'test_client_id', got '%s'", config.GoogleClientID)
		}
		if config.GoogleClientSecret != "test_client_secret" {
			t.Errorf("Expected GoogleClientSecret to be 'test_client_secret', got '%s'", config.GoogleClientSecret)
		}
		if config.GoogleRedirectURL != "http://localhost:8080/auth/google/callback" {
			t.Errorf("Expected GoogleRedirectURL to be default, got '%s'", config.GoogleRedirectURL)
		}
		if config.GeminiAPIKey != "test_gemini_key" {
			t.Errorf("Expected GeminiAPIKey to be 'test_gemini_key', got '%s'", config.GeminiAPIKey)
		}
		if config.Environment != "development" {
			t.Errorf("Expected Environment to be 'development', got '%s'", config.Environment)
		}
	})

	t.Run("loads config with custom env vars", func(t *testing.T) {
		os.Setenv("PORT", "9000")
		os.Setenv("SESSION_SECRET", "custom_secret")
		os.Setenv("GOOGLE_CLIENT_ID", "custom_client_id")
		os.Setenv("GOOGLE_CLIENT_SECRET", "custom_client_secret")
		os.Setenv("GOOGLE_REDIRECT_URL", "https://example.com/callback")
		os.Setenv("GEMINI_API_KEY", "custom_gemini_key")
		os.Setenv("ENVIRONMENT", "production")

		config := Load()

		if config.Port != "9000" {
			t.Errorf("Expected Port to be '9000', got '%s'", config.Port)
		}
		if config.SessionSecret != "custom_secret" {
			t.Errorf("Expected SessionSecret to be 'custom_secret', got '%s'", config.SessionSecret)
		}
		if config.Environment != "production" {
			t.Errorf("Expected Environment to be 'production', got '%s'", config.Environment)
		}
	})
}

func TestLoadValidation(t *testing.T) {
	// Save original environment
	originalEnv := map[string]string{
		"GOOGLE_CLIENT_ID":     os.Getenv("GOOGLE_CLIENT_ID"),
		"GOOGLE_CLIENT_SECRET": os.Getenv("GOOGLE_CLIENT_SECRET"),
		"GEMINI_API_KEY":       os.Getenv("GEMINI_API_KEY"),
	}

	// Clean up environment
	for key := range originalEnv {
		os.Unsetenv(key)
	}

	// Restore environment after test
	defer func() {
		for key, value := range originalEnv {
			if value != "" {
				os.Setenv(key, value)
			} else {
				os.Unsetenv(key)
			}
		}
	}()

	tests := []struct {
		name           string
		clientID       string
		clientSecret   string
		geminiKey      string
		shouldPanic    bool
		expectedErrMsg string
	}{
		{
			name:         "valid config",
			clientID:     "test_id",
			clientSecret: "test_secret",
			geminiKey:    "test_key",
			shouldPanic:  false,
		},
		{
			name:           "missing client ID",
			clientID:       "",
			clientSecret:   "test_secret",
			geminiKey:      "test_key",
			shouldPanic:    true,
			expectedErrMsg: "GOOGLE_CLIENT_ID environment variable is required",
		},
		{
			name:           "missing client secret",
			clientID:       "test_id",
			clientSecret:   "",
			geminiKey:      "test_key",
			shouldPanic:    true,
			expectedErrMsg: "GOOGLE_CLIENT_SECRET environment variable is required",
		},
		{
			name:           "missing gemini key",
			clientID:       "test_id",
			clientSecret:   "test_secret",
			geminiKey:      "",
			shouldPanic:    true,
			expectedErrMsg: "GEMINI_API_KEY environment variable is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variables
			if tt.clientID != "" {
				os.Setenv("GOOGLE_CLIENT_ID", tt.clientID)
			}
			if tt.clientSecret != "" {
				os.Setenv("GOOGLE_CLIENT_SECRET", tt.clientSecret)
			}
			if tt.geminiKey != "" {
				os.Setenv("GEMINI_API_KEY", tt.geminiKey)
			}

			if tt.shouldPanic {
				defer func() {
					if r := recover(); r == nil {
						t.Errorf("Expected Load() to panic, but it didn't")
					}
				}()
				Load()
			} else {
				Load()
			}

			// Clean up for next test
			os.Unsetenv("GOOGLE_CLIENT_ID")
			os.Unsetenv("GOOGLE_CLIENT_SECRET")
			os.Unsetenv("GEMINI_API_KEY")
		})
	}
}
