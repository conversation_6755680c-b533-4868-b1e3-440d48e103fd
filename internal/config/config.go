package config

import (
	"log"
	"os"

	"github.com/joho/godotenv"
)

type Config struct {
	Port                string
	SessionSecret       string
	GoogleClientID      string
	GoogleClientSecret  string
	GoogleRedirectURL   string
	GeminiAPIKey        string
	Environment         string
}

func Load() *Config {
	// Load .env file if it exists
	if err := godotenv.Load(); err != nil {
		log.Println("No .env file found, using environment variables")
	}

	config := &Config{
		Port:                getEnv("PORT", "8080"),
		SessionSecret:       getEnv("SESSION_SECRET", "your-secret-key-change-this"),
		GoogleClientID:      getEnv("GOOGLE_CLIENT_ID", ""),
		GoogleClientSecret:  getEnv("GOOGLE_CLIENT_SECRET", ""),
		GoogleRedirectURL:   getEnv("GOOGLE_REDIRECT_URL", "http://localhost:8080/auth/google/callback"),
		GeminiAPIKey:        getEnv("GEMINI_API_KEY", ""),
		Environment:         getEnv("ENVIRONMENT", "development"),
	}

	// Validate required environment variables
	if config.GoogleClientID == "" {
		panic("GOOGLE_CLIENT_ID environment variable is required")
	}
	if config.GoogleClientSecret == "" {
		panic("GOOGLE_CLIENT_SECRET environment variable is required")
	}
	if config.GeminiAPIKey == "" {
		panic("GEMINI_API_KEY environment variable is required")
	}

	return config
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
