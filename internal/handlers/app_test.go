package handlers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"os"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/config"
	"github.com/stefanoschrs/mealpal/internal/models"
	"github.com/stefanoschrs/mealpal/internal/services"
)

func TestNewAppHandler(t *testing.T) {
	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAppHandler(foodLogService, authService, store)

	if handler == nil {
		t.Fatal("Expected NewAppHandler to return a non-nil handler")
	}

	if handler.foodLogService != foodLogService {
		t.Error("Expected foodLogService to be set correctly")
	}

	if handler.authService != authService {
		t.Error("Expected authService to be set correctly")
	}

	if handler.sessionStore != store {
		t.Error("Expected sessionStore to be set correctly")
	}
}

func TestAppHandler_Home(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAppHandler(foodLogService, authService, store)

	// Create a test router
	router := gin.New()
	router.LoadHTMLGlob("../../templates/*")
	router.GET("/", handler.Home)

	// Create a request
	req := httptest.NewRequest("GET", "/", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should return OK
	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	// Should contain the title
	body := w.Body.String()
	if !strings.Contains(body, "MealPal") {
		t.Error("Expected response to contain 'MealPal'")
	}
}

func TestAppHandler_Error(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAppHandler(foodLogService, authService, store)

	// Create a test router
	router := gin.New()
	router.LoadHTMLGlob("../../templates/*")
	router.GET("/error", handler.Error)

	tests := []struct {
		name          string
		errorParam    string
		expectedError string
	}{
		{
			name:          "with error parameter",
			errorParam:    "Test error message",
			expectedError: "Test error message",
		},
		{
			name:          "without error parameter",
			errorParam:    "",
			expectedError: "An unknown error occurred",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a request with error parameter
			reqURL := "/error"
			if tt.errorParam != "" {
				reqURL += "?error=" + url.QueryEscape(tt.errorParam)
			}

			req := httptest.NewRequest("GET", reqURL, nil)
			w := httptest.NewRecorder()

			router.ServeHTTP(w, req)

			// Should return Internal Server Error
			if w.Code != http.StatusInternalServerError {
				t.Errorf("Expected status %d, got %d", http.StatusInternalServerError, w.Code)
			}

			// Should contain the error message
			body := w.Body.String()
			if !strings.Contains(body, tt.expectedError) {
				t.Errorf("Expected response to contain '%s'", tt.expectedError)
			}
		})
	}
}

func TestAppHandler_Dashboard_NoUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAppHandler(foodLogService, authService, store)

	// Create a test router
	router := gin.New()
	router.GET("/dashboard", handler.Dashboard)

	// Create a request without user in context
	req := httptest.NewRequest("GET", "/dashboard", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should redirect to home
	if w.Code != http.StatusFound {
		t.Errorf("Expected status %d, got %d", http.StatusFound, w.Code)
	}

	location := w.Header().Get("Location")
	if location != "/" {
		t.Errorf("Expected redirect to '/', got '%s'", location)
	}
}

func TestAppHandler_Dashboard_WithUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAppHandler(foodLogService, authService, store)

	// Create a test router
	router := gin.New()
	router.LoadHTMLGlob("../../templates/*")
	router.GET("/dashboard", func(c *gin.Context) {
		// Set user in context
		user := &models.User{
			ID:    "test123",
			Email: "<EMAIL>",
			Name:  "Test User",
		}
		c.Set("user", user)
		handler.Dashboard(c)
	})

	// Create a request
	req := httptest.NewRequest("GET", "/dashboard", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should return OK
	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	// Should contain dashboard content
	body := w.Body.String()
	if !strings.Contains(body, "Dashboard") {
		t.Error("Expected response to contain 'Dashboard'")
	}
}

func TestAppHandler_GetSpreadsheetInfo_NoUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAppHandler(foodLogService, authService, store)

	// Create a test router
	router := gin.New()
	router.GET("/api/spreadsheet-info", handler.GetSpreadsheetInfo)

	// Create a request without user in context
	req := httptest.NewRequest("GET", "/api/spreadsheet-info", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should return Unauthorized
	if w.Code != http.StatusUnauthorized {
		t.Errorf("Expected status %d, got %d", http.StatusUnauthorized, w.Code)
	}

	// Parse response
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to parse response: %v", err)
	}

	if response["success"] != false {
		t.Error("Expected success to be false")
	}

	if !strings.Contains(response["error"].(string), "not authenticated") {
		t.Error("Expected error message about authentication")
	}
}

func TestAppHandler_GetSpreadsheetInfo_NoSpreadsheet(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAppHandler(foodLogService, authService, store)

	// Create a test router
	router := gin.New()
	router.GET("/api/spreadsheet-info", func(c *gin.Context) {
		// Set user in context without spreadsheet ID
		user := &models.User{
			ID:            "test123",
			Email:         "<EMAIL>",
			Name:          "Test User",
			SpreadsheetID: "", // No spreadsheet
		}
		c.Set("user", user)
		handler.GetSpreadsheetInfo(c)
	})

	// Create a request
	req := httptest.NewRequest("GET", "/api/spreadsheet-info", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should return OK
	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	// Parse response
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to parse response: %v", err)
	}

	if response["success"] != true {
		t.Error("Expected success to be true")
	}

	if response["hasSpreadsheet"] != false {
		t.Error("Expected hasSpreadsheet to be false")
	}
}

func TestAppHandler_GetSpreadsheetInfo_WithSpreadsheet(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAppHandler(foodLogService, authService, store)

	// Create a test router
	router := gin.New()
	router.GET("/api/spreadsheet-info", func(c *gin.Context) {
		// Set user in context with spreadsheet ID
		user := &models.User{
			ID:            "test123",
			Email:         "<EMAIL>",
			Name:          "Test User",
			SpreadsheetID: "test_spreadsheet_123",
		}
		c.Set("user", user)
		handler.GetSpreadsheetInfo(c)
	})

	// Create a request
	req := httptest.NewRequest("GET", "/api/spreadsheet-info", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should return OK
	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	// Parse response
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to parse response: %v", err)
	}

	if response["success"] != true {
		t.Error("Expected success to be true")
	}

	if response["hasSpreadsheet"] != true {
		t.Error("Expected hasSpreadsheet to be true")
	}

	expectedURL := "https://docs.google.com/spreadsheets/d/test_spreadsheet_123/edit"
	if response["spreadsheetURL"] != expectedURL {
		t.Errorf("Expected spreadsheetURL to be %s, got %s", expectedURL, response["spreadsheetURL"])
	}
}
