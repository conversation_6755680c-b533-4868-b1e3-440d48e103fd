package handlers

import (
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/config"
	"github.com/stefanoschrs/mealpal/internal/models"
	"github.com/stefanoschrs/mealpal/internal/services"
)

func TestNewAuthHandler(t *testing.T) {
	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	if handler == nil {
		t.Fatal("Expected NewAuthHandler to return a non-nil handler")
	}

	if handler.authService != authService {
		t.Error("Expected authService to be set correctly")
	}

	if handler.store != store {
		t.Error("Expected store to be set correctly")
	}

	if handler.foodLogService != foodLogService {
		t.Error("Expected foodLogService to be set correctly")
	}
}

func TestAuthHandler_Login(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Create a test router
	router := gin.New()
	router.GET("/auth/google/login", handler.Login)

	// Create a request
	req := httptest.NewRequest("GET", "/auth/google/login", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should redirect to Google OAuth
	if w.Code != http.StatusFound {
		t.Errorf("Expected status %d, got %d", http.StatusFound, w.Code)
	}

	location := w.Header().Get("Location")
	if !strings.Contains(location, "accounts.google.com") {
		t.Error("Expected redirect to Google OAuth URL")
	}

	if !strings.Contains(location, "client_id=test_client_id") {
		t.Error("Expected redirect URL to contain client ID")
	}

	// Check that state parameter is present
	if !strings.Contains(location, "state=") {
		t.Error("Expected redirect URL to contain state parameter")
	}
}

func TestAuthHandler_Callback_NoState(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Create a test router with HTML templates
	router := gin.New()
	router.LoadHTMLGlob("../../templates/*")
	router.GET("/auth/google/callback", handler.Callback)

	// Create a request without state parameter
	req := httptest.NewRequest("GET", "/auth/google/callback?code=test_code", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should return error page
	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
	}
}

func TestAuthHandler_Callback_NoCode(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Create a test router with HTML templates
	router := gin.New()
	router.LoadHTMLGlob("../../templates/*")
	router.GET("/auth/google/callback", handler.Callback)

	// Create a request without code parameter
	req := httptest.NewRequest("GET", "/auth/google/callback?state=test_state", nil)
	w := httptest.NewRecorder()

	// Set up session with state
	session, _ := store.Get(req, "mealpal-session")
	session.Values["oauth_state"] = "test_state"
	session.Save(req, w)

	// Create new request with session cookie
	cookies := w.Result().Cookies()
	req2 := httptest.NewRequest("GET", "/auth/google/callback?state=test_state", nil)
	for _, cookie := range cookies {
		req2.AddCookie(cookie)
	}
	w2 := httptest.NewRecorder()

	router.ServeHTTP(w2, req2)

	// Should return error page
	if w2.Code != http.StatusBadRequest {
		t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w2.Code)
	}
}

func TestAuthHandler_Logout(t *testing.T) {
	gin.SetMode(gin.TestMode)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Create a test router
	router := gin.New()
	router.GET("/auth/logout", handler.Logout)

	// Create a request
	req := httptest.NewRequest("GET", "/auth/logout", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	// Should redirect to home
	if w.Code != http.StatusFound {
		t.Errorf("Expected status %d, got %d", http.StatusFound, w.Code)
	}

	location := w.Header().Get("Location")
	if location != "/" {
		t.Errorf("Expected redirect to '/', got '%s'", location)
	}
}

func TestGenerateStateToken(t *testing.T) {
	token1 := generateStateToken()
	token2 := generateStateToken()

	if token1 == "" {
		t.Error("Expected generateStateToken to return a non-empty token")
	}

	if token2 == "" {
		t.Error("Expected generateStateToken to return a non-empty token")
	}

	if token1 == token2 {
		t.Error("Expected generateStateToken to return different tokens on each call")
	}

	// Token should be base64 encoded
	if len(token1) < 40 { // 32 bytes base64 encoded should be at least 44 characters, but allowing some margin
		t.Errorf("Expected token to be at least 40 characters, got %d", len(token1))
	}
}

func TestAuthHandler_MergeUserData_NewUser(t *testing.T) {
	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	authService := services.NewAuthService(cfg)
	store := sessions.NewCookieStore([]byte("test-secret"))
	
	geminiService := services.NewGeminiService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	foodLogService := services.NewFoodLogService(geminiService, sheetsService, userStore)

	handler := NewAuthHandler(authService, store, foodLogService)

	// Test with new user (not in store)
	googleUser := &models.User{
		ID:           "new_user_123",
		Email:        "<EMAIL>",
		Name:         "New User",
		Picture:      "https://example.com/pic.jpg",
		AccessToken:  "new_access_token",
		RefreshToken: "new_refresh_token",
	}

	mergedUser := handler.mergeUserData(googleUser)

	// Should return the same user since it's new
	if mergedUser.ID != googleUser.ID {
		t.Errorf("Expected ID %s, got %s", googleUser.ID, mergedUser.ID)
	}

	if mergedUser.Email != googleUser.Email {
		t.Errorf("Expected Email %s, got %s", googleUser.Email, mergedUser.Email)
	}

	if mergedUser.SpreadsheetID != "" {
		t.Errorf("Expected empty SpreadsheetID for new user, got %s", mergedUser.SpreadsheetID)
	}
}
