package handlers

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/middleware"
	"github.com/stefanoschrs/mealpal/internal/models"
	"github.com/stefanoschrs/mealpal/internal/services"
	"golang.org/x/oauth2"
)

type AuthHandler struct {
	authService    *services.AuthService
	store          sessions.Store
	foodLogService *services.FoodLogService
}

func NewAuthHandler(authService *services.AuthService, store sessions.Store, foodLogService *services.FoodLogService) *AuthHandler {
	return &AuthHandler{
		authService:    authService,
		store:          store,
		foodLogService: foodLogService,
	}
}

func (h *AuthHandler) Login(c *gin.Context) {
	// Generate a random state token
	state := generateStateToken()
	
	// Store state in session for verification
	session, _ := h.store.Get(c.Request, middleware.SessionName)
	session.Values["oauth_state"] = state
	session.Save(c.Request, c.Writer)

	// Redirect to Google OAuth
	authURL := h.authService.GetAuthURL(state)
	c.Redirect(http.StatusFound, authURL)
}

func (h *AuthHandler) Callback(c *gin.Context) {
	// Verify state token
	session, err := h.store.Get(c.Request, middleware.SessionName)
	if err != nil {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Session error",
		})
		return
	}

	storedState, ok := session.Values["oauth_state"].(string)
	if !ok || storedState != c.Query("state") {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "Invalid state token",
		})
		return
	}

	// Exchange code for token
	code := c.Query("code")
	if code == "" {
		c.HTML(http.StatusBadRequest, "error.html", gin.H{
			"error": "No authorization code received",
		})
		return
	}

	var token *oauth2.Token
	token, err = h.authService.ExchangeCode(c.Request.Context(), code)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to exchange authorization code",
		})
		return
	}

	// Get user info from Google
	user, err := h.authService.GetUserInfo(c.Request.Context(), token)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to get user information",
		})
		return
	}

	// Check if this is an existing user and merge stored data
	finalUser := h.mergeUserData(user)

	// For new users, create spreadsheet immediately
	if finalUser.SpreadsheetID == "" {
		err = h.createSpreadsheetForUser(c.Request.Context(), finalUser, token)
		if err != nil {
			// Log error but don't fail login - spreadsheet can be created later
			fmt.Printf("Warning: Failed to create spreadsheet for new user %s: %v\n", finalUser.Email, err)
		}
	}

	// Store user and token in session
	sessionData := models.SessionData{
		User:       finalUser,
		OAuthToken: token,
		IsLoggedIn: true,
	}

	sessionDataJSON, err := json.Marshal(sessionData)
	if err != nil {
		c.HTML(http.StatusInternalServerError, "error.html", gin.H{
			"error": "Failed to save session",
		})
		return
	}

	session.Values["session_data"] = string(sessionDataJSON)
	session.Save(c.Request, c.Writer)

	// Redirect to dashboard
	c.Redirect(http.StatusFound, "/dashboard")
}

func (h *AuthHandler) Logout(c *gin.Context) {
	session, _ := h.store.Get(c.Request, middleware.SessionName)
	
	// Clear session
	session.Values = make(map[interface{}]interface{})
	session.Options.MaxAge = -1
	session.Save(c.Request, c.Writer)

	c.Redirect(http.StatusFound, "/")
}

// mergeUserData merges fresh Google user data with existing stored user data
func (h *AuthHandler) mergeUserData(googleUser *models.User) *models.User {
	// Check if user exists in store
	storedUser, exists := h.foodLogService.GetUserStore().GetUser(googleUser.ID)
	if exists {
		// Merge: use stored data but update with fresh Google info
		storedUser.Email = googleUser.Email
		storedUser.Name = googleUser.Name
		storedUser.Picture = googleUser.Picture
		storedUser.AccessToken = googleUser.AccessToken
		storedUser.RefreshToken = googleUser.RefreshToken
		// Keep existing SpreadsheetID

		// Save updated user data
		h.foodLogService.GetUserStore().SaveUser(storedUser)
		fmt.Printf("DEBUG: Existing user %s logged in, SpreadsheetID: %s\n", storedUser.Email, storedUser.SpreadsheetID)
		return storedUser
	}

	// New user - save to store
	h.foodLogService.GetUserStore().SaveUser(googleUser)
	fmt.Printf("DEBUG: New user %s signed up\n", googleUser.Email)
	return googleUser
}

// createSpreadsheetForUser creates a spreadsheet for a new user
func (h *AuthHandler) createSpreadsheetForUser(ctx context.Context, user *models.User, token *oauth2.Token) error {
	// Use the sheets service to create an empty spreadsheet
	sheetsService := services.NewSheetsService(h.authService.GetOAuthConfig(), h.foodLogService.GetUserStore())

	err := sheetsService.CreateSpreadsheetForUser(ctx, user, token)
	if err != nil {
		return fmt.Errorf("failed to create initial spreadsheet: %w", err)
	}

	fmt.Printf("DEBUG: Created spreadsheet for new user %s\n", user.Email)
	return nil
}

func generateStateToken() string {
	b := make([]byte, 32)
	rand.Read(b)
	return base64.URLEncoding.EncodeToString(b)
}
