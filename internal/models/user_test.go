package models

import (
	"encoding/json"
	"testing"
	"time"

	"golang.org/x/oauth2"
)

func TestUserJSONMarshaling(t *testing.T) {
	user := &User{
		ID:            "123",
		Email:         "<EMAIL>",
		Name:          "Test User",
		Picture:       "https://example.com/pic.jpg",
		AccessToken:   "access_token",
		RefreshToken:  "refresh_token",
		SpreadsheetID: "spreadsheet_123",
	}

	// Test marshaling
	data, err := json.Marshal(user)
	if err != nil {
		t.Fatalf("Failed to marshal user: %v", err)
	}

	// Test unmarshaling
	var unmarshaled User
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal user: %v", err)
	}

	// Verify fields (note: AccessToken and RefreshToken should be excluded due to json:"-" tag)
	if unmarshaled.ID != user.ID {
		t.<PERSON>rrorf("Expected ID %s, got %s", user.ID, unmarshaled.ID)
	}
	if unmarshaled.Email != user.Email {
		t.Errorf("Expected Email %s, got %s", user.Email, unmarshaled.Email)
	}
	if unmarshaled.Name != user.Name {
		t.Errorf("Expected Name %s, got %s", user.Name, unmarshaled.Name)
	}
	if unmarshaled.Picture != user.Picture {
		t.Errorf("Expected Picture %s, got %s", user.Picture, unmarshaled.Picture)
	}
	if unmarshaled.SpreadsheetID != user.SpreadsheetID {
		t.Errorf("Expected SpreadsheetID %s, got %s", user.SpreadsheetID, unmarshaled.SpreadsheetID)
	}

	// AccessToken and RefreshToken should be empty due to json:"-" tag
	if unmarshaled.AccessToken != "" {
		t.Errorf("Expected AccessToken to be empty, got %s", unmarshaled.AccessToken)
	}
	if unmarshaled.RefreshToken != "" {
		t.Errorf("Expected RefreshToken to be empty, got %s", unmarshaled.RefreshToken)
	}
}

func TestFoodEntryJSONMarshaling(t *testing.T) {
	entry := &FoodEntry{
		UserID:    "user123",
		Text:      "I ate an apple",
		ParsedCSV: "2023-01-01,12:00,Apple,1,80,0,0,20,Fresh",
		Timestamp: time.Now().Format(time.RFC3339),
	}

	// Test marshaling
	data, err := json.Marshal(entry)
	if err != nil {
		t.Fatalf("Failed to marshal food entry: %v", err)
	}

	// Test unmarshaling
	var unmarshaled FoodEntry
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal food entry: %v", err)
	}

	// Verify fields
	if unmarshaled.UserID != entry.UserID {
		t.Errorf("Expected UserID %s, got %s", entry.UserID, unmarshaled.UserID)
	}
	if unmarshaled.Text != entry.Text {
		t.Errorf("Expected Text %s, got %s", entry.Text, unmarshaled.Text)
	}
	if unmarshaled.ParsedCSV != entry.ParsedCSV {
		t.Errorf("Expected ParsedCSV %s, got %s", entry.ParsedCSV, unmarshaled.ParsedCSV)
	}
	if unmarshaled.Timestamp != entry.Timestamp {
		t.Errorf("Expected Timestamp %s, got %s", entry.Timestamp, unmarshaled.Timestamp)
	}
}

func TestGoogleUserInfoJSONMarshaling(t *testing.T) {
	googleUser := &GoogleUserInfo{
		ID:      "google123",
		Email:   "<EMAIL>",
		Name:    "Google User",
		Picture: "https://lh3.googleusercontent.com/pic",
	}

	// Test marshaling
	data, err := json.Marshal(googleUser)
	if err != nil {
		t.Fatalf("Failed to marshal google user info: %v", err)
	}

	// Test unmarshaling
	var unmarshaled GoogleUserInfo
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal google user info: %v", err)
	}

	// Verify fields
	if unmarshaled.ID != googleUser.ID {
		t.Errorf("Expected ID %s, got %s", googleUser.ID, unmarshaled.ID)
	}
	if unmarshaled.Email != googleUser.Email {
		t.Errorf("Expected Email %s, got %s", googleUser.Email, unmarshaled.Email)
	}
	if unmarshaled.Name != googleUser.Name {
		t.Errorf("Expected Name %s, got %s", googleUser.Name, unmarshaled.Name)
	}
	if unmarshaled.Picture != googleUser.Picture {
		t.Errorf("Expected Picture %s, got %s", googleUser.Picture, unmarshaled.Picture)
	}
}

func TestSessionDataJSONMarshaling(t *testing.T) {
	user := &User{
		ID:    "123",
		Email: "<EMAIL>",
		Name:  "Test User",
	}

	token := &oauth2.Token{
		AccessToken:  "access_token",
		RefreshToken: "refresh_token",
		TokenType:    "Bearer",
	}

	sessionData := &SessionData{
		User:       user,
		OAuthToken: token,
		IsLoggedIn: true,
	}

	// Test marshaling
	data, err := json.Marshal(sessionData)
	if err != nil {
		t.Fatalf("Failed to marshal session data: %v", err)
	}

	// Test unmarshaling
	var unmarshaled SessionData
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal session data: %v", err)
	}

	// Verify fields
	if unmarshaled.IsLoggedIn != sessionData.IsLoggedIn {
		t.Errorf("Expected IsLoggedIn %v, got %v", sessionData.IsLoggedIn, unmarshaled.IsLoggedIn)
	}

	if unmarshaled.User == nil {
		t.Fatal("Expected User to be non-nil")
	}
	if unmarshaled.User.ID != user.ID {
		t.Errorf("Expected User ID %s, got %s", user.ID, unmarshaled.User.ID)
	}

	if unmarshaled.OAuthToken == nil {
		t.Fatal("Expected OAuthToken to be non-nil")
	}
	if unmarshaled.OAuthToken.AccessToken != token.AccessToken {
		t.Errorf("Expected AccessToken %s, got %s", token.AccessToken, unmarshaled.OAuthToken.AccessToken)
	}
}

func TestUserStructFields(t *testing.T) {
	user := User{
		ID:            "test-id",
		Email:         "<EMAIL>",
		Name:          "Test User",
		Picture:       "https://example.com/pic.jpg",
		AccessToken:   "access-token",
		RefreshToken:  "refresh-token",
		SpreadsheetID: "sheet-id",
	}

	// Test that all fields are accessible
	if user.ID != "test-id" {
		t.Errorf("Expected ID to be 'test-id', got '%s'", user.ID)
	}
	if user.Email != "<EMAIL>" {
		t.Errorf("Expected Email to be '<EMAIL>', got '%s'", user.Email)
	}
	if user.Name != "Test User" {
		t.Errorf("Expected Name to be 'Test User', got '%s'", user.Name)
	}
	if user.Picture != "https://example.com/pic.jpg" {
		t.Errorf("Expected Picture to be 'https://example.com/pic.jpg', got '%s'", user.Picture)
	}
	if user.AccessToken != "access-token" {
		t.Errorf("Expected AccessToken to be 'access-token', got '%s'", user.AccessToken)
	}
	if user.RefreshToken != "refresh-token" {
		t.Errorf("Expected RefreshToken to be 'refresh-token', got '%s'", user.RefreshToken)
	}
	if user.SpreadsheetID != "sheet-id" {
		t.Errorf("Expected SpreadsheetID to be 'sheet-id', got '%s'", user.SpreadsheetID)
	}
}

func TestFoodEntryStructFields(t *testing.T) {
	entry := FoodEntry{
		UserID:    "user-123",
		Text:      "I ate a banana",
		ParsedCSV: "2023-01-01,12:00,Banana,1,105,1,0,27,Yellow",
		Timestamp: "2023-01-01T12:00:00Z",
	}

	// Test that all fields are accessible
	if entry.UserID != "user-123" {
		t.Errorf("Expected UserID to be 'user-123', got '%s'", entry.UserID)
	}
	if entry.Text != "I ate a banana" {
		t.Errorf("Expected Text to be 'I ate a banana', got '%s'", entry.Text)
	}
	if entry.ParsedCSV != "2023-01-01,12:00,Banana,1,105,1,0,27,Yellow" {
		t.Errorf("Expected ParsedCSV to match, got '%s'", entry.ParsedCSV)
	}
	if entry.Timestamp != "2023-01-01T12:00:00Z" {
		t.Errorf("Expected Timestamp to be '2023-01-01T12:00:00Z', got '%s'", entry.Timestamp)
	}
}
