package models

import "golang.org/x/oauth2"

type User struct {
	ID            string `json:"id"`
	Email         string `json:"email"`
	Name          string `json:"name"`
	Picture       string `json:"picture"`
	AccessToken   string `json:"-"`
	RefreshToken  string `json:"-"`
	SpreadsheetID string `json:"spreadsheet_id,omitempty"`
}

type FoodEntry struct {
	UserID    string `json:"user_id"`
	Text      string `json:"text"`
	ParsedCSV string `json:"parsed_csv"`
	Timestamp string `json:"timestamp"`
}

type GoogleUserInfo struct {
	ID      string `json:"id"`
	Email   string `json:"email"`
	Name    string `json:"name"`
	Picture string `json:"picture"`
}

type SessionData struct {
	User        *User        `json:"user"`
	OAuthToken  *oauth2.Token `json:"oauth_token"`
	IsLoggedIn  bool         `json:"is_logged_in"`
}
