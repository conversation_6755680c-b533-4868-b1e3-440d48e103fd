package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/stefanoschrs/mealpal/internal/config"
)

type GeminiService struct {
	apiKey string
	client *http.Client
}

type GeminiRequest struct {
	Contents []GeminiContent `json:"contents"`
}

type GeminiContent struct {
	Parts []GeminiPart `json:"parts"`
}

type GeminiPart struct {
	Text string `json:"text"`
}

type GeminiResponse struct {
	Candidates []GeminiCandidate `json:"candidates"`
}

type GeminiCandidate struct {
	Content GeminiContent `json:"content"`
}

func NewGeminiService(cfg *config.Config) *GeminiService {
	return &GeminiService{
		apiKey: cfg.GeminiAPIKey,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (s *GeminiService) ParseFoodText(foodText string) (string, error) {
	prompt := fmt.Sprintf(`
Please analyze the following food description and convert it to CSV format with EXACTLY these 9 columns:
Date, Time, Food Item, Quantity, Estimated Calories, Protein (g), Fat (g), Carbohydrates (g), Notes

STRICT FORMATTING RULES:
- Date: Use format YYYY-MM-DD (today: %s)
- Time: Use format HH:MM (current time if not specified)
- Food Item: ONLY the basic food name (e.g., "Rice", "Chicken breast", "Apple")
- Quantity: ONLY the amount with unit (e.g., "1 cup", "100g", "1 medium", "2 slices")
- Estimated Calories: ONLY the number (e.g., "205", "165")
- Protein (g): ONLY the number (e.g., "4", "31")
- Fat (g): ONLY the number (e.g., "0", "3.6")
- Carbohydrates (g): ONLY the number (e.g., "45", "0")
- Notes: Put preparation methods, cooking style, brands here (e.g., "white, cooked", "grilled", "Granny Smith")

CRITICAL: Each column must contain ONLY what is specified above. Do NOT put cooking methods in Food Item or Quantity columns.

Examples of CORRECT format:
2025-06-08,14:30,Rice,1 cup,205,4,0,45,"white, cooked"
2025-06-08,14:30,Chicken breast,100g,165,31,3.6,0,grilled

Break down complex meals into separate rows for each food item.
Return ONLY the CSV data, no headers, no explanations.

Food description: %s
`, time.Now().Format("2006-01-02"), foodText)

	reqBody := GeminiRequest{
		Contents: []GeminiContent{
			{
				Parts: []GeminiPart{
					{Text: prompt},
				},
			},
		},
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	url := fmt.Sprintf("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=%s", s.apiKey)

	resp, err := s.client.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to call Gemini API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Gemini API error (status %d): %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	var geminiResp GeminiResponse
	if err := json.Unmarshal(body, &geminiResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if len(geminiResp.Candidates) == 0 || len(geminiResp.Candidates[0].Content.Parts) == 0 {
		return "", fmt.Errorf("no response from Gemini API")
	}

	csvData := strings.TrimSpace(geminiResp.Candidates[0].Content.Parts[0].Text)

	// Clean and validate the CSV data
	cleanedCSV := s.cleanAndValidateCSV(csvData)
	return cleanedCSV, nil
}

// cleanAndValidateCSV cleans up the Gemini response and ensures proper CSV format
func (s *GeminiService) cleanAndValidateCSV(csvData string) string {
	lines := strings.Split(csvData, "\n")
	var cleanedLines []string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Skip any explanatory text or headers
		if strings.Contains(strings.ToLower(line), "date") && strings.Contains(strings.ToLower(line), "time") {
			continue // Skip header line
		}
		if strings.HasPrefix(line, "Here") || strings.HasPrefix(line, "The") || strings.HasPrefix(line, "Based") {
			continue // Skip explanatory text
		}

		// Clean up the line
		cleanedLine := s.cleanCSVLine(line)
		if cleanedLine != "" {
			cleanedLines = append(cleanedLines, cleanedLine)
		}
	}

	return strings.Join(cleanedLines, "\n")
}

// cleanCSVLine ensures a single CSV line has proper format
func (s *GeminiService) cleanCSVLine(line string) string {
	// Remove any markdown formatting
	line = strings.ReplaceAll(line, "`", "")
	line = strings.ReplaceAll(line, "*", "")

	// Split by comma to check field count
	fields := strings.Split(line, ",")

	// If we don't have enough fields, skip this line
	if len(fields) < 5 {
		fmt.Printf("Warning: Skipping malformed line with %d fields: %s\n", len(fields), line)
		return ""
	}

	// Clean each field
	for i, field := range fields {
		field = strings.TrimSpace(field)
		field = strings.Trim(field, "\"")
		fields[i] = field
	}

	// Ensure we have exactly 9 fields
	for len(fields) < 9 {
		fields = append(fields, "")
	}
	if len(fields) > 9 {
		fields = fields[:9]
	}

	// Rebuild the line with proper quoting
	var quotedFields []string
	for _, field := range fields {
		// Quote fields that contain commas or special characters
		if strings.Contains(field, ",") || strings.Contains(field, "\"") || strings.Contains(field, "\n") {
			field = "\"" + strings.ReplaceAll(field, "\"", "\"\"") + "\""
		}
		quotedFields = append(quotedFields, field)
	}

	return strings.Join(quotedFields, ",")
}
