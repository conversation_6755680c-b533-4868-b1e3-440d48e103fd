package services

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stefanoschrs/mealpal/internal/config"
)

func TestNewGeminiService(t *testing.T) {
	cfg := &config.Config{
		GeminiAPIKey: "test_api_key",
	}

	service := NewGeminiService(cfg)

	if service == nil {
		t.Fatal("Expected NewGeminiService to return a non-nil service")
	}

	if service.apiKey != cfg.GeminiAPIKey {
		t.Errorf("Expected apiKey to be %s, got %s", cfg.GeminiAPIKey, service.apiKey)
	}

	if service.client == nil {
		t.Fatal("Expected client to be initialized")
	}

	// Check timeout is set
	if service.client.Timeout.Seconds() != 30 {
		t.Errorf("Expected client timeout to be 30 seconds, got %v", service.client.Timeout)
	}
}

func TestGeminiService_ParseFoodText_Success(t *testing.T) {
	// Create a mock Gemini API server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Verify request method and path
		if r.Method != "POST" {
			t.Errorf("Expected POST request, got %s", r.Method)
		}

		if !strings.Contains(r.URL.Path, "generateContent") {
			t.Errorf("Expected path to contain 'generateContent', got %s", r.URL.Path)
		}

		// Verify API key in query params
		apiKey := r.URL.Query().Get("key")
		if apiKey != "test_api_key" {
			t.Errorf("Expected API key 'test_api_key', got '%s'", apiKey)
		}

		// Verify Content-Type header
		contentType := r.Header.Get("Content-Type")
		if contentType != "application/json" {
			t.Errorf("Expected Content-Type 'application/json', got '%s'", contentType)
		}

		// Parse request body
		var request GeminiRequest
		if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
			t.Fatalf("Failed to decode request body: %v", err)
		}

		// Verify request structure
		if len(request.Contents) != 1 {
			t.Errorf("Expected 1 content, got %d", len(request.Contents))
		}

		if len(request.Contents[0].Parts) != 1 {
			t.Errorf("Expected 1 part, got %d", len(request.Contents[0].Parts))
		}

		// Verify the prompt contains the food text
		prompt := request.Contents[0].Parts[0].Text
		if !strings.Contains(prompt, "I ate an apple") {
			t.Error("Expected prompt to contain food text")
		}

		// Mock successful response
		response := GeminiResponse{
			Candidates: []GeminiCandidate{
				{
					Content: GeminiContent{
						Parts: []GeminiPart{
							{
								Text: "2023-01-01,12:00,Apple,1 medium,80,0,0,21,Fresh red apple",
							},
						},
					},
				},
			},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	cfg := &config.Config{
		GeminiAPIKey: "test_api_key",
	}

	service := NewGeminiService(cfg)

	// Use the test server URL
	result, err := service.parseWithCustomURL("I ate an apple", server.URL)

	if err != nil {
		t.Fatalf("Expected ParseFoodText to succeed, got error: %v", err)
	}

	expected := "2023-01-01,12:00,Apple,1 medium,80,0,0,21,Fresh red apple"
	if result != expected {
		t.Errorf("Expected result '%s', got '%s'", expected, result)
	}

	// Test with original URL (will fail but we can test the URL construction)
	_, err = service.ParseFoodText("test food")
	if err == nil {
		t.Error("Expected ParseFoodText to fail with invalid API key")
	}

	// Verify the error contains expected information (could be API key error or parse error)
	if !strings.Contains(err.Error(), "failed to parse food text") && !strings.Contains(err.Error(), "API key not valid") {
		t.Errorf("Expected error to contain 'failed to parse food text' or 'API key not valid', got: %v", err)
	}
}

func TestGeminiService_ParseFoodText_EmptyResponse(t *testing.T) {
	// Create a mock server that returns empty candidates
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := GeminiResponse{
			Candidates: []GeminiCandidate{},
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	cfg := &config.Config{
		GeminiAPIKey: "test_api_key",
	}

	service := NewGeminiService(cfg)

	result, err := service.parseWithCustomURL("test food", server.URL)

	if err == nil {
		t.Fatal("Expected ParseFoodText to fail with empty response")
	}

	if !strings.Contains(err.Error(), "no response from Gemini") {
		t.Errorf("Expected error to contain 'no response from Gemini', got: %v", err)
	}

	if result != "" {
		t.Errorf("Expected empty result, got '%s'", result)
	}
}

func TestGeminiService_ParseFoodText_HTTPError(t *testing.T) {
	// Create a mock server that returns HTTP error
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Internal Server Error"))
	}))
	defer server.Close()

	cfg := &config.Config{
		GeminiAPIKey: "test_api_key",
	}

	service := NewGeminiService(cfg)

	result, err := service.parseWithCustomURL("test food", server.URL)

	if err == nil {
		t.Fatal("Expected ParseFoodText to fail with HTTP error")
	}

	if !strings.Contains(err.Error(), "HTTP error") {
		t.Errorf("Expected error to contain 'HTTP error', got: %v", err)
	}

	if result != "" {
		t.Errorf("Expected empty result, got '%s'", result)
	}
}

func TestGeminiService_ParseFoodText_InvalidJSON(t *testing.T) {
	// Create a mock server that returns invalid JSON
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.Write([]byte("invalid json"))
	}))
	defer server.Close()

	cfg := &config.Config{
		GeminiAPIKey: "test_api_key",
	}

	service := NewGeminiService(cfg)

	result, err := service.parseWithCustomURL("test food", server.URL)

	if err == nil {
		t.Fatal("Expected ParseFoodText to fail with invalid JSON")
	}

	if !strings.Contains(err.Error(), "failed to decode response") {
		t.Errorf("Expected error to contain 'failed to decode response', got: %v", err)
	}

	if result != "" {
		t.Errorf("Expected empty result, got '%s'", result)
	}
}

func TestGeminiService_ParseFoodText_NetworkError(t *testing.T) {
	cfg := &config.Config{
		GeminiAPIKey: "test_api_key",
	}

	service := NewGeminiService(cfg)

	// Use an invalid URL to simulate network error
	result, err := service.parseWithCustomURL("test food", "http://invalid-url-that-does-not-exist.com")

	if err == nil {
		t.Fatal("Expected ParseFoodText to fail with network error")
	}

	if result != "" {
		t.Errorf("Expected empty result, got '%s'", result)
	}
}

func TestGeminiRequest_Structure(t *testing.T) {
	// Test the structure of GeminiRequest
	request := GeminiRequest{
		Contents: []GeminiContent{
			{
				Parts: []GeminiPart{
					{
						Text: "test text",
					},
				},
			},
		},
	}

	// Test JSON marshaling
	data, err := json.Marshal(request)
	if err != nil {
		t.Fatalf("Failed to marshal GeminiRequest: %v", err)
	}

	// Test JSON unmarshaling
	var unmarshaled GeminiRequest
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal GeminiRequest: %v", err)
	}

	// Verify structure
	if len(unmarshaled.Contents) != 1 {
		t.Errorf("Expected 1 content, got %d", len(unmarshaled.Contents))
	}

	if len(unmarshaled.Contents[0].Parts) != 1 {
		t.Errorf("Expected 1 part, got %d", len(unmarshaled.Contents[0].Parts))
	}

	if unmarshaled.Contents[0].Parts[0].Text != "test text" {
		t.Errorf("Expected text 'test text', got '%s'", unmarshaled.Contents[0].Parts[0].Text)
	}
}

func TestGeminiResponse_Structure(t *testing.T) {
	// Test the structure of GeminiResponse
	response := GeminiResponse{
		Candidates: []GeminiCandidate{
			{
				Content: GeminiContent{
					Parts: []GeminiPart{
						{
							Text: "response text",
						},
					},
				},
			},
		},
	}

	// Test JSON marshaling
	data, err := json.Marshal(response)
	if err != nil {
		t.Fatalf("Failed to marshal GeminiResponse: %v", err)
	}

	// Test JSON unmarshaling
	var unmarshaled GeminiResponse
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal GeminiResponse: %v", err)
	}

	// Verify structure
	if len(unmarshaled.Candidates) != 1 {
		t.Errorf("Expected 1 candidate, got %d", len(unmarshaled.Candidates))
	}

	if len(unmarshaled.Candidates[0].Content.Parts) != 1 {
		t.Errorf("Expected 1 part, got %d", len(unmarshaled.Candidates[0].Content.Parts))
	}

	if unmarshaled.Candidates[0].Content.Parts[0].Text != "response text" {
		t.Errorf("Expected text 'response text', got '%s'", unmarshaled.Candidates[0].Content.Parts[0].Text)
	}
}
