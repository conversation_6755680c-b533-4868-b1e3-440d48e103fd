package services

import (
	"context"
	"fmt"
	"strings"

	"github.com/stefanoschrs/mealpal/internal/models"
	"golang.org/x/oauth2"
	"google.golang.org/api/option"
	"google.golang.org/api/sheets/v4"
)

type SheetsService struct {
	oauthConfig *oauth2.Config
	userStore   *UserStore
}

func NewSheetsService(oauthConfig *oauth2.Config, userStore *UserStore) *SheetsService {
	return &SheetsService{
		oauthConfig: oauthConfig,
		userStore:   userStore,
	}
}

func (s *SheetsService) AddFoodEntry(ctx context.Context, user *models.User, token *oauth2.Token, csvData string) error {
	client := s.oauthConfig.Client(ctx, token)

	service, err := sheets.NewService(ctx, option.WithHTTPClient(client))
	if err != nil {
		return fmt.Errorf("failed to create sheets service: %w", err)
	}

	// Get or create the MealPal spreadsheet
	spreadsheetID, err := s.getOrCreateSpreadsheet(service, user)
	if err != nil {
		return fmt.Errorf("failed to get/create spreadsheet: %w", err)
	}

	// Parse CSV data and add rows
	rows := s.parseCSVToRows(csvData)
	if len(rows) == 0 {
		return fmt.Errorf("no valid data to add")
	}

	// Add the rows to the spreadsheet (now with 9 columns including macros)
	valueRange := &sheets.ValueRange{
		Values: rows,
	}

	_, err = service.Spreadsheets.Values.Append(
		spreadsheetID,
		"Sheet1!A:I", // 9 columns: Date, Time, Food Item, Quantity, Calories, Protein, Fat, Carbs, Notes
		valueRange,
	).ValueInputOption("USER_ENTERED").Do()

	if err != nil {
		return fmt.Errorf("failed to append data to spreadsheet: %w", err)
	}

	return nil
}

// CreateSpreadsheetForUser creates an empty spreadsheet for a user
func (s *SheetsService) CreateSpreadsheetForUser(ctx context.Context, user *models.User, token *oauth2.Token) error {
	client := s.oauthConfig.Client(ctx, token)

	service, err := sheets.NewService(ctx, option.WithHTTPClient(client))
	if err != nil {
		return fmt.Errorf("failed to create sheets service: %w", err)
	}

	// Create the spreadsheet (this will also save the ID to user store)
	_, err = s.getOrCreateSpreadsheet(service, user)
	if err != nil {
		return fmt.Errorf("failed to create spreadsheet: %w", err)
	}

	return nil
}

func (s *SheetsService) getOrCreateSpreadsheet(service *sheets.Service, user *models.User) (string, error) {
	// Check if user already has a spreadsheet ID stored
	storedUser, exists := s.userStore.GetUser(user.ID)
	if exists && storedUser.SpreadsheetID != "" {
		// Verify the spreadsheet still exists
		_, err := service.Spreadsheets.Get(storedUser.SpreadsheetID).Do()
		if err == nil {
			// Spreadsheet exists, return the ID
			fmt.Printf("♻️  Reusing existing spreadsheet for %s: %s\n", user.Email, storedUser.SpreadsheetID)
			return storedUser.SpreadsheetID, nil
		}
		// If spreadsheet doesn't exist anymore, we'll create a new one below
	}

	// Create a new spreadsheet
	spreadsheet := &sheets.Spreadsheet{
		Properties: &sheets.SpreadsheetProperties{
			Title: "MealPal Food Log",
		},
		Sheets: []*sheets.Sheet{
			{
				Properties: &sheets.SheetProperties{
					Title: "Sheet1",
				},
			},
		},
	}

	resp, err := service.Spreadsheets.Create(spreadsheet).Do()
	if err != nil {
		return "", err
	}

	// Add headers to the new spreadsheet (now with macro columns)
	headers := [][]interface{}{
		{"Date", "Time", "Food Item", "Quantity", "Estimated Calories", "Protein (g)", "Fat (g)", "Carbohydrates (g)", "Notes"},
	}

	headerRange := &sheets.ValueRange{
		Values: headers,
	}

	_, err = service.Spreadsheets.Values.Update(
		resp.SpreadsheetId,
		"Sheet1!A1:I1", // Updated to include all 9 columns
		headerRange,
	).ValueInputOption("USER_ENTERED").Do()

	if err != nil {
		return "", fmt.Errorf("failed to add headers: %w", err)
	}

	// Store the spreadsheet ID for future use
	user.SpreadsheetID = resp.SpreadsheetId
	s.userStore.SaveUser(user)

	fmt.Printf("✅ Created new spreadsheet for %s: %s\n", user.Email, resp.SpreadsheetId)
	return resp.SpreadsheetId, nil
}

func (s *SheetsService) parseCSVToRows(csvData string) [][]interface{} {
	lines := strings.Split(strings.TrimSpace(csvData), "\n")
	var rows [][]interface{}

	for lineNum, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}

		// Parse CSV line properly handling quoted fields
		fields := s.parseCSVLine(line)

		// Validate we have exactly 9 columns
		if len(fields) != 9 {
			fmt.Printf("Warning: Line %d has %d columns instead of 9, padding/truncating: %s\n", lineNum+1, len(fields), line)
			// Ensure we have exactly 9 fields
			for len(fields) < 9 {
				fields = append(fields, "")
			}
			if len(fields) > 9 {
				fields = fields[:9]
			}
		}

		row := make([]interface{}, 9)
		for i, field := range fields {
			if i < 9 {
				row[i] = strings.TrimSpace(field)
			}
		}
		rows = append(rows, row)
	}

	return rows
}

// parseCSVLine properly parses a CSV line handling quoted fields
func (s *SheetsService) parseCSVLine(line string) []string {
	var fields []string
	var current strings.Builder
	inQuotes := false

	for i := 0; i < len(line); i++ {
		char := line[i]
		switch char {
		case '"':
			if inQuotes && i+1 < len(line) && line[i+1] == '"' {
				// Escaped quote
				current.WriteRune('"')
				i++ // Skip next quote
			} else {
				// Toggle quote state
				inQuotes = !inQuotes
			}
		case ',':
			if inQuotes {
				current.WriteRune(char)
			} else {
				// End of field
				fields = append(fields, strings.Trim(current.String(), "\""))
				current.Reset()
			}
		default:
			current.WriteRune(char)
		}
	}

	// Add the last field
	fields = append(fields, strings.Trim(current.String(), "\""))

	return fields
}
