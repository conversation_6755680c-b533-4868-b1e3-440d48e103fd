package services

import (
	"context"
	"os"
	"strings"
	"testing"

	"github.com/stefanoschrs/mealpal/internal/config"
	"github.com/stefanoschrs/mealpal/internal/models"
	"golang.org/x/oauth2"
)

func TestNewFoodLogService(t *testing.T) {
	cfg := &config.Config{
		GeminiAPIKey: "test_api_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	geminiService := NewGeminiService(cfg)
	userStore := NewUserStore()
	oauthConfig := &oauth2.Config{
		ClientID:     "test_client_id",
		ClientSecret: "test_client_secret",
	}
	sheetsService := NewSheetsService(oauthConfig, userStore)

	foodLogService := NewFoodLogService(geminiService, sheetsService, userStore)

	if foodLogService == nil {
		t.Fatal("Expected NewFoodLogService to return a non-nil service")
	}

	if foodLogService.geminiService != geminiService {
		t.Error("Expected geminiService to be set correctly")
	}

	if foodLogService.sheetsService != sheetsService {
		t.Error("Expected sheetsService to be set correctly")
	}

	if foodLogService.userStore != userStore {
		t.Error("Expected userStore to be set correctly")
	}
}

func TestFoodLogService_GetUserStore(t *testing.T) {
	cfg := &config.Config{
		GeminiAPIKey: "test_api_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	geminiService := NewGeminiService(cfg)
	userStore := NewUserStore()
	oauthConfig := &oauth2.Config{
		ClientID:     "test_client_id",
		ClientSecret: "test_client_secret",
	}
	sheetsService := NewSheetsService(oauthConfig, userStore)

	foodLogService := NewFoodLogService(geminiService, sheetsService, userStore)

	returnedUserStore := foodLogService.GetUserStore()

	if returnedUserStore != userStore {
		t.Error("Expected GetUserStore to return the same userStore instance")
	}
}

func TestFoodLogService_LogFood_ErrorCases(t *testing.T) {
	cfg := &config.Config{
		GeminiAPIKey: "invalid_api_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	geminiService := NewGeminiService(cfg)
	userStore := NewUserStore()
	oauthConfig := &oauth2.Config{
		ClientID:     "test_client_id",
		ClientSecret: "test_client_secret",
	}
	sheetsService := NewSheetsService(oauthConfig, userStore)

	foodLogService := NewFoodLogService(geminiService, sheetsService, userStore)

	user := &models.User{
		ID:    "test123",
		Email: "<EMAIL>",
		Name:  "Test User",
	}

	token := &oauth2.Token{
		AccessToken: "invalid_token",
	}

	ctx := context.Background()

	// Test with invalid API key (will fail at Gemini API)
	entry, err := foodLogService.LogFood(ctx, user, token, "I ate an apple")

	if err == nil {
		t.Error("Expected LogFood to fail with invalid API key")
	}

	if entry != nil {
		t.Error("Expected entry to be nil on error")
	}

	// Verify error message
	if !strings.Contains(err.Error(), "failed to parse food text") {
		t.Errorf("Expected error about failed to parse food text, got: %v", err)
	}

	// Verify user was saved to store even on error
	storedUser, exists := userStore.GetUser(user.ID)
	if !exists {
		t.Error("Expected user to be saved to store even on error")
	}

	if storedUser.Email != user.Email {
		t.Errorf("Expected stored user email %s, got %s", user.Email, storedUser.Email)
	}
}

func TestFoodLogService_LogFood_UserStorage(t *testing.T) {
	cfg := &config.Config{
		GeminiAPIKey: "test_api_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	geminiService := NewGeminiService(cfg)
	userStore := NewUserStore()
	oauthConfig := &oauth2.Config{
		ClientID:     "test_client_id",
		ClientSecret: "test_client_secret",
	}
	sheetsService := NewSheetsService(oauthConfig, userStore)

	foodLogService := NewFoodLogService(geminiService, sheetsService, userStore)

	user := &models.User{
		ID:    "test123",
		Email: "<EMAIL>",
		Name:  "Test User",
	}

	token := &oauth2.Token{
		AccessToken: "test_token",
	}

	ctx := context.Background()

	// This will fail at the API level, but we can test user storage
	foodLogService.LogFood(ctx, user, token, "I ate an apple")

	// Verify user was saved to store
	storedUser, exists := userStore.GetUser(user.ID)
	if !exists {
		t.Error("Expected user to be saved to store")
	}

	if storedUser.Email != user.Email {
		t.Errorf("Expected stored user email %s, got %s", user.Email, storedUser.Email)
	}

	if storedUser.Name != user.Name {
		t.Errorf("Expected stored user name %s, got %s", user.Name, storedUser.Name)
	}
}

func TestFoodLogService_Integration(t *testing.T) {
	// This test verifies the integration between components without external API calls
	cfg := &config.Config{
		GeminiAPIKey: "test_api_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	geminiService := NewGeminiService(cfg)
	userStore := NewUserStore()
	oauthConfig := &oauth2.Config{
		ClientID:     "test_client_id",
		ClientSecret: "test_client_secret",
	}
	sheetsService := NewSheetsService(oauthConfig, userStore)

	foodLogService := NewFoodLogService(geminiService, sheetsService, userStore)

	// Test that all components are properly initialized
	if foodLogService.geminiService == nil {
		t.Error("Expected geminiService to be initialized")
	}

	if foodLogService.sheetsService == nil {
		t.Error("Expected sheetsService to be initialized")
	}

	if foodLogService.userStore == nil {
		t.Error("Expected userStore to be initialized")
	}

	// Test that GetUserStore returns the correct instance
	returnedStore := foodLogService.GetUserStore()
	if returnedStore != userStore {
		t.Error("Expected GetUserStore to return the same userStore instance")
	}

	// Test user storage functionality
	user := &models.User{
		ID:    "integration_test",
		Email: "<EMAIL>",
		Name:  "Integration User",
	}

	// Save user directly to store
	userStore.SaveUser(user)

	// Retrieve user through foodLogService
	retrievedUser, exists := foodLogService.GetUserStore().GetUser(user.ID)
	if !exists {
		t.Error("Expected user to exist in store")
	}

	if retrievedUser.Email != user.Email {
		t.Errorf("Expected email %s, got %s", user.Email, retrievedUser.Email)
	}
}

func TestFoodLogService_StructFields(t *testing.T) {
	cfg := &config.Config{
		GeminiAPIKey: "test_api_key",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	geminiService := NewGeminiService(cfg)
	userStore := NewUserStore()
	oauthConfig := &oauth2.Config{
		ClientID:     "test_client_id",
		ClientSecret: "test_client_secret",
	}
	sheetsService := NewSheetsService(oauthConfig, userStore)

	foodLogService := NewFoodLogService(geminiService, sheetsService, userStore)

	// Test that the struct fields are accessible and correct
	if foodLogService.geminiService != geminiService {
		t.Error("geminiService field not set correctly")
	}

	if foodLogService.sheetsService != sheetsService {
		t.Error("sheetsService field not set correctly")
	}

	if foodLogService.userStore != userStore {
		t.Error("userStore field not set correctly")
	}
}

func TestFoodLogService_NilInputs(t *testing.T) {
	// Test behavior with nil inputs
	foodLogService := NewFoodLogService(nil, nil, nil)

	if foodLogService == nil {
		t.Fatal("Expected NewFoodLogService to return a non-nil service even with nil inputs")
	}

	// GetUserStore should handle nil userStore gracefully
	userStore := foodLogService.GetUserStore()
	if userStore != nil {
		t.Error("Expected GetUserStore to return nil when userStore is nil")
	}
}
