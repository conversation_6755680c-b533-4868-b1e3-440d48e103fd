package services

import (
	"context"
	"os"
	"strings"
	"testing"

	"github.com/stefanoschrs/mealpal/internal/models"
	"golang.org/x/oauth2"
)

func TestNewSheetsService(t *testing.T) {
	oauthConfig := &oauth2.Config{
		ClientID:     "test_client_id",
		ClientSecret: "test_client_secret",
	}

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	userStore := NewUserStore()

	service := NewSheetsService(oauthConfig, userStore)

	if service == nil {
		t.Fatal("Expected NewSheetsService to return a non-nil service")
	}

	if service.oauthConfig != oauthConfig {
		t.Error("Expected oauthConfig to be set correctly")
	}

	if service.userStore != userStore {
		t.Error("Expected userStore to be set correctly")
	}
}

func TestSheetsService_ParseCSVLine(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	userStore := NewUserStore()
	service := NewSheetsService(&oauth2.Config{}, userStore)

	tests := []struct {
		name     string
		input    string
		expected []string
	}{
		{
			name:     "simple CSV line",
			input:    "2023-01-01,12:00,Apple,1 medium,80,0,0,21,Fresh",
			expected: []string{"2023-01-01", "12:00", "Apple", "1 medium", "80", "0", "0", "21", "Fresh"},
		},
		{
			name:     "CSV line with quoted field",
			input:    `2023-01-01,12:00,Rice,1 cup,205,4,0,45,"white, cooked"`,
			expected: []string{"2023-01-01", "12:00", "Rice", "1 cup", "205", "4", "0", "45", "white, cooked"},
		},
		{
			name:     "CSV line with escaped quotes",
			input:    `2023-01-01,12:00,Bread,2 slices,160,6,2,30,"whole ""grain"" bread"`,
			expected: []string{"2023-01-01", "12:00", "Bread", "2 slices", "160", "6", "2", "30", `whole "grain" bread`},
		},
		{
			name:     "CSV line with commas in quoted field",
			input:    `2023-01-01,12:00,Salad,1 bowl,150,5,8,15,"lettuce, tomato, cucumber"`,
			expected: []string{"2023-01-01", "12:00", "Salad", "1 bowl", "150", "5", "8", "15", "lettuce, tomato, cucumber"},
		},
		{
			name:     "empty fields",
			input:    "2023-01-01,12:00,Apple,,80,,,21,",
			expected: []string{"2023-01-01", "12:00", "Apple", "", "80", "", "", "21", ""},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.parseCSVLine(tt.input)

			if len(result) != len(tt.expected) {
				t.Errorf("Expected %d fields, got %d", len(tt.expected), len(result))
				return
			}

			for i, expected := range tt.expected {
				if result[i] != expected {
					t.Errorf("Field %d: expected '%s', got '%s'", i, expected, result[i])
				}
			}
		})
	}
}

func TestSheetsService_ParseCSVToRows(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	userStore := NewUserStore()
	service := NewSheetsService(&oauth2.Config{}, userStore)

	tests := []struct {
		name     string
		input    string
		expected int // number of rows expected
	}{
		{
			name:     "single line CSV",
			input:    "2023-01-01,12:00,Apple,1 medium,80,0,0,21,Fresh",
			expected: 1,
		},
		{
			name: "multi-line CSV",
			input: `2023-01-01,12:00,Apple,1 medium,80,0,0,21,Fresh
2023-01-01,12:30,Banana,1 medium,105,1,0,27,Yellow`,
			expected: 2,
		},
		{
			name:     "CSV with empty lines",
			input:    "2023-01-01,12:00,Apple,1 medium,80,0,0,21,Fresh\n\n\n2023-01-01,12:30,Banana,1 medium,105,1,0,27,Yellow",
			expected: 2,
		},
		{
			name:     "empty CSV",
			input:    "",
			expected: 0,
		},
		{
			name:     "CSV with only whitespace",
			input:    "   \n  \n  ",
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.parseCSVToRows(tt.input)

			if len(result) != tt.expected {
				t.Errorf("Expected %d rows, got %d", tt.expected, len(result))
			}

			// Verify each row has exactly 9 columns
			for i, row := range result {
				if len(row) != 9 {
					t.Errorf("Row %d: expected 9 columns, got %d", i, len(row))
				}
			}
		})
	}
}

func TestSheetsService_ParseCSVToRows_FieldPadding(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	userStore := NewUserStore()
	service := NewSheetsService(&oauth2.Config{}, userStore)

	// Test with insufficient fields (should be padded)
	input := "2023-01-01,12:00,Apple,1 medium,80"
	result := service.parseCSVToRows(input)

	if len(result) != 1 {
		t.Fatalf("Expected 1 row, got %d", len(result))
	}

	row := result[0]
	if len(row) != 9 {
		t.Errorf("Expected 9 columns after padding, got %d", len(row))
	}

	// Check that the missing fields are empty strings
	for i := 5; i < 9; i++ {
		if row[i] != "" {
			t.Errorf("Expected empty string at index %d, got '%v'", i, row[i])
		}
	}

	// Test with too many fields (should be truncated)
	input = "2023-01-01,12:00,Apple,1 medium,80,0,0,21,Fresh,Extra1,Extra2,Extra3"
	result = service.parseCSVToRows(input)

	if len(result) != 1 {
		t.Fatalf("Expected 1 row, got %d", len(result))
	}

	row = result[0]
	if len(row) != 9 {
		t.Errorf("Expected 9 columns after truncation, got %d", len(row))
	}
}

func TestSheetsService_AddFoodEntry_ErrorCases(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	userStore := NewUserStore()
	oauthConfig := &oauth2.Config{
		ClientID:     "test_client_id",
		ClientSecret: "test_client_secret",
	}
	service := NewSheetsService(oauthConfig, userStore)

	user := &models.User{
		ID:    "test123",
		Email: "<EMAIL>",
		Name:  "Test User",
	}

	token := &oauth2.Token{
		AccessToken: "invalid_token",
	}

	ctx := context.Background()

	// Test with empty CSV data
	err := service.AddFoodEntry(ctx, user, token, "")
	if err == nil {
		t.Error("Expected AddFoodEntry to fail with empty CSV data")
	}
	// The error could be either "no valid data to add" or a Google Sheets API error
	if !strings.Contains(err.Error(), "no valid data to add") && !strings.Contains(err.Error(), "failed to get/create spreadsheet") {
		t.Errorf("Expected error about no valid data or spreadsheet creation, got: %v", err)
	}

	// Test with invalid CSV data
	err = service.AddFoodEntry(ctx, user, token, "invalid,csv")
	if err == nil {
		t.Error("Expected AddFoodEntry to fail with invalid credentials")
	}
	// This will fail at the Google Sheets API level due to invalid credentials
}

func TestSheetsService_CreateSpreadsheetForUser_ErrorCase(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	userStore := NewUserStore()
	oauthConfig := &oauth2.Config{
		ClientID:     "test_client_id",
		ClientSecret: "test_client_secret",
	}
	service := NewSheetsService(oauthConfig, userStore)

	user := &models.User{
		ID:    "test123",
		Email: "<EMAIL>",
		Name:  "Test User",
	}

	token := &oauth2.Token{
		AccessToken: "invalid_token",
	}

	ctx := context.Background()

	// This will fail with invalid credentials
	err := service.CreateSpreadsheetForUser(ctx, user, token)
	if err == nil {
		t.Error("Expected CreateSpreadsheetForUser to fail with invalid credentials")
	}

	if !strings.Contains(err.Error(), "failed to create spreadsheet") {
		t.Errorf("Expected error about failed to create spreadsheet, got: %v", err)
	}
}

func TestSheetsService_ParseCSVLine_EdgeCases(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	userStore := NewUserStore()
	service := NewSheetsService(&oauth2.Config{}, userStore)

	tests := []struct {
		name     string
		input    string
		expected int // expected number of fields
	}{
		{
			name:     "empty string",
			input:    "",
			expected: 1, // Empty string results in one empty field
		},
		{
			name:     "single comma",
			input:    ",",
			expected: 2, // Two empty fields
		},
		{
			name:     "multiple commas",
			input:    ",,,,",
			expected: 5, // Five empty fields
		},
		{
			name:     "quoted empty field",
			input:    `"",test,"",another`,
			expected: 4,
		},
		{
			name:     "field with only spaces",
			input:    "   ,  test  ,   ",
			expected: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.parseCSVLine(tt.input)

			if len(result) != tt.expected {
				t.Errorf("Expected %d fields, got %d", tt.expected, len(result))
			}
		})
	}
}
