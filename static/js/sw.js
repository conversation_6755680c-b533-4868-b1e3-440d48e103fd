// MealPal Service Worker - PWA functionality

const CACHE_NAME = 'mealpal-v1';
const STATIC_CACHE_URLS = [
    '/',
    '/static/css/style.css',
    '/static/js/app.js',
    '/static/manifest.json'
];

// Install event - cache essential resources
self.addEventListener('install', function(event) {
    console.log('Service Worker installing...');
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                console.log('Caching essential resources');
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(function() {
                console.log('Service Worker installed successfully');
                return self.skipWaiting();
            })
    );
});

// Activate event - clean up old caches and take control
self.addEventListener('activate', function(event) {
    console.log('Service Worker activating...');
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(function() {
            console.log('Service Worker activated successfully');
            return self.clients.claim();
        })
    );
});

// Fetch event - network first with cache fallback for static resources
self.addEventListener('fetch', function(event) {
    // For navigation requests, always go to network
    if (event.request.mode === 'navigate') {
        event.respondWith(fetch(event.request));
        return;
    }

    // For static resources, try network first, fallback to cache
    event.respondWith(
        fetch(event.request)
            .then(function(response) {
                // If we got a valid response, update the cache
                if (response.status === 200) {
                    const responseClone = response.clone();
                    caches.open(CACHE_NAME).then(function(cache) {
                        cache.put(event.request, responseClone);
                    });
                }
                return response;
            })
            .catch(function() {
                // Network failed, try cache
                return caches.match(event.request);
            })
    );
});
