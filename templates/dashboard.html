<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4CAF50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="MealPal">

    <!-- PWA Icons -->
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="/static/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/static/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/static/icons/icon-512x512.png">

    <!-- <PERSON><PERSON> Manifest -->
    <link rel="manifest" href="/static/manifest.json">

    <!-- CSS -->
    <link rel="stylesheet" href="/static/css/style.css">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Navigation Bar -->
        <nav class="navbar">
            <div class="nav-left">
                <h1 class="logo">🍽️ MealPal</h1>
            </div>
            <div class="nav-right">
                <div class="user-menu">
                    <img src="{{.user.Picture}}" alt="{{.user.Name}}" class="user-avatar" id="userAvatar">
                    <div class="dropdown-menu" id="dropdownMenu">
                        <div class="dropdown-header">
                            <img src="{{.user.Picture}}" alt="{{.user.Name}}" class="dropdown-avatar">
                            <div class="dropdown-user-info">
                                <div class="dropdown-name">{{.user.Name}}</div>
                                <div class="dropdown-email">{{.user.Email}}</div>
                            </div>
                        </div>
                        <div class="dropdown-divider"></div>
                        <a href="#" id="spreadsheetLink" class="dropdown-item" style="display: none;" target="_blank">
                            <span class="dropdown-icon">📊</span>
                            View Spreadsheet
                        </a>
                        <a href="/auth/logout" class="dropdown-item">
                            <span class="dropdown-icon">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-container">
                <div class="welcome-section">
                    <h2>Welcome back, {{.user.Name}}! 👋</h2>
                    <p>What did you eat today? Describe your meal and I'll log it for you.</p>
                </div>

                <!-- Food Input Form -->
                <div class="food-form-container">
                    <form id="foodForm" class="food-form">
                        <div class="form-group">
                            <label for="foodText">Describe your meal:</label>
                            <textarea
                                id="foodText"
                                name="food_text"
                                placeholder="e.g., I had a chicken caesar salad with grilled chicken breast, romaine lettuce, parmesan cheese, and caesar dressing for lunch at 1:30 PM"
                                rows="4"
                                required
                            ></textarea>
                        </div>
                        <button type="submit" class="submit-btn" id="submitBtn">
                            <span class="btn-text">Log Food</span>
                            <span class="btn-spinner" style="display: none;">
                                <div class="spinner"></div>
                            </span>
                        </button>
                    </form>
                </div>

                <!-- Tips Section -->
                <div class="tips-section">
                    <h3>💡 Tips for better logging:</h3>
                    <ul class="tips-list">
                        <li>Include specific quantities (e.g., "1 cup of rice", "2 slices of bread")</li>
                        <li>Mention cooking methods (e.g., "grilled", "fried", "steamed")</li>
                        <li>Add timing if different from now (e.g., "for breakfast at 8 AM")</li>
                        <li>Include brands or restaurant names when relevant</li>
                        <li>AI will automatically calculate calories and macros (protein, fat, carbohydrates)</li>
                        <li>Data is saved to your personal Google Sheets for easy tracking</li>
                    </ul>
                </div>
            </div>
        </main>
    </div>

    <!-- Toast Container -->
    <div id="toast-container"></div>

    <!-- JavaScript -->
    <script src="/static/js/app.js"></script>

    <!-- PWA Service Worker -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/static/js/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }
    </script>
</body>
</html>
