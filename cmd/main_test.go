package main

import (
	"os"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestMain(t *testing.T) {
	// Save original environment
	originalEnv := map[string]string{
		"PORT":                 os.Getenv("PORT"),
		"SESSION_SECRET":       os.<PERSON><PERSON><PERSON>("SESSION_SECRET"),
		"GOOGLE_CLIENT_ID":     os.<PERSON>env("GOOGLE_CLIENT_ID"),
		"GOOGLE_CLIENT_SECRET": os.<PERSON>env("GOOGLE_CLIENT_SECRET"),
		"GOOGLE_REDIRECT_URL":  os.<PERSON>env("GOOGLE_REDIRECT_URL"),
		"GEMINI_API_KEY":       os.<PERSON>env("GEMINI_API_KEY"),
		"ENVIRONMENT":          os.Getenv("ENVIRONMENT"),
	}

	// Clean up environment
	for key := range originalEnv {
		os.Unsetenv(key)
	}

	// Restore environment after test
	defer func() {
		for key, value := range originalEnv {
			if value != "" {
				os.Setenv(key, value)
			} else {
				os.Unsetenv(key)
			}
		}
	}()

	// Set required environment variables for testing
	os.Setenv("GOOGLE_CLIENT_ID", "test_client_id")
	os.Setenv("GOOGLE_CLIENT_SECRET", "test_client_secret")
	os.Setenv("GEMINI_API_KEY", "test_gemini_key")
	os.Setenv("PORT", "8080")
	os.Setenv("ENVIRONMENT", "test")

	// Set Gin to test mode to avoid starting actual server
	gin.SetMode(gin.TestMode)

	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	// Test that main function can be called without panicking
	// We can't actually test the server startup without modifying main(),
	// but we can test that the initialization doesn't panic
	defer func() {
		if r := recover(); r != nil {
			// If we get a panic about binding to port (which is expected in tests),
			// that's actually good - it means we got through all the initialization
			if panicMsg, ok := r.(string); ok {
				if panicMsg == "listen tcp :8080: bind: address already in use" ||
					panicMsg == "Failed to start server:" {
					// This is expected in test environment
					return
				}
			}
			// Re-panic if it's an unexpected error
			panic(r)
		}
	}()

	// We can't actually call main() directly because it will try to start a server
	// Instead, we'll test the individual components that main() would initialize

	// This test mainly ensures that the imports and basic setup work
	// The actual functionality is tested in the individual package tests
}

func TestGinModeSettings(t *testing.T) {
	// Test that Gin mode is set correctly based on environment
	originalMode := gin.Mode()
	defer gin.SetMode(originalMode)

	// Save original environment
	originalEnv := os.Getenv("ENVIRONMENT")
	defer func() {
		if originalEnv != "" {
			os.Setenv("ENVIRONMENT", originalEnv)
		} else {
			os.Unsetenv("ENVIRONMENT")
		}
	}()

	tests := []struct {
		name        string
		environment string
		expectedMode string
	}{
		{
			name:        "production environment",
			environment: "production",
			expectedMode: gin.ReleaseMode,
		},
		{
			name:        "development environment",
			environment: "development",
			expectedMode: gin.DebugMode,
		},
		{
			name:        "test environment",
			environment: "test",
			expectedMode: gin.TestMode,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			os.Setenv("ENVIRONMENT", tt.environment)

			// Reset Gin mode
			gin.SetMode(gin.DebugMode)

			// Test the logic that would be in main()
			if tt.environment == "production" {
				gin.SetMode(gin.ReleaseMode)
			} else if tt.environment == "test" {
				gin.SetMode(gin.TestMode)
			}

			if gin.Mode() != tt.expectedMode {
				t.Errorf("Expected Gin mode %s, got %s", tt.expectedMode, gin.Mode())
			}
		})
	}
}

func TestEnvironmentVariableHandling(t *testing.T) {
	// This test verifies that the application properly handles environment variables
	// We test the logic that main() would use without actually calling main()

	tests := []struct {
		name           string
		clientID       string
		clientSecret   string
		geminiKey      string
		shouldHaveError bool
	}{
		{
			name:           "valid environment variables",
			clientID:       "test_client_id",
			clientSecret:   "test_client_secret",
			geminiKey:      "test_gemini_key",
			shouldHaveError: false,
		},
		{
			name:           "missing client ID",
			clientID:       "",
			clientSecret:   "test_client_secret",
			geminiKey:      "test_gemini_key",
			shouldHaveError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test the validation logic that main() would use
			hasError := false
			if tt.clientID == "" || tt.clientSecret == "" || tt.geminiKey == "" {
				hasError = true
			}

			if hasError != tt.shouldHaveError {
				t.Errorf("Expected hasError=%v, got %v", tt.shouldHaveError, hasError)
			}
		})
	}
}

func TestSessionStoreConfiguration(t *testing.T) {
	// Test session store configuration logic that would be in main()
	
	tests := []struct {
		name        string
		environment string
		expectedSecure bool
	}{
		{
			name:        "production environment should use secure cookies",
			environment: "production",
			expectedSecure: true,
		},
		{
			name:        "development environment should not use secure cookies",
			environment: "development",
			expectedSecure: false,
		},
		{
			name:        "test environment should not use secure cookies",
			environment: "test",
			expectedSecure: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test the logic that would be in main() for session configuration
			secure := tt.environment == "production"
			
			if secure != tt.expectedSecure {
				t.Errorf("Expected secure=%v for environment %s, got %v", 
					tt.expectedSecure, tt.environment, secure)
			}
		})
	}
}

func TestRouteConfiguration(t *testing.T) {
	// Test that we can create the router structure that main() would create
	gin.SetMode(gin.TestMode)

	router := gin.New()

	// Test public routes
	router.GET("/", func(c *gin.Context) { c.String(200, "home") })
	router.GET("/error", func(c *gin.Context) { c.String(200, "error") })

	// Test auth routes
	auth := router.Group("/auth")
	{
		auth.GET("/google/login", func(c *gin.Context) { c.String(200, "login") })
		auth.GET("/google/callback", func(c *gin.Context) { c.String(200, "callback") })
		auth.GET("/logout", func(c *gin.Context) { c.String(200, "logout") })
	}

	// Test protected routes
	protected := router.Group("/")
	// Note: In real main(), this would use middleware.AuthRequired(store)
	{
		protected.GET("/dashboard", func(c *gin.Context) { c.String(200, "dashboard") })
		protected.POST("/submit-food", func(c *gin.Context) { c.String(200, "submit") })
		protected.GET("/api/spreadsheet-info", func(c *gin.Context) { c.String(200, "info") })
	}

	// Verify that the router was created successfully
	if router == nil {
		t.Error("Expected router to be created successfully")
	}

	// Test that routes are accessible (basic smoke test)
	routes := router.Routes()
	if len(routes) == 0 {
		t.Error("Expected routes to be registered")
	}

	// Verify we have the expected number of routes
	expectedRoutes := 7 // home, error, login, callback, logout, dashboard, submit-food, spreadsheet-info
	if len(routes) < expectedRoutes {
		t.Errorf("Expected at least %d routes, got %d", expectedRoutes, len(routes))
	}
}

func TestStaticFileConfiguration(t *testing.T) {
	// Test static file serving configuration that would be in main()
	gin.SetMode(gin.TestMode)

	router := gin.New()
	
	// Test the static file configuration that main() would use
	router.Static("/static", "./static")

	// Verify that the static route was configured
	routes := router.Routes()
	
	// Look for static file route
	hasStaticRoute := false
	for _, route := range routes {
		if route.Path == "/static/*filepath" {
			hasStaticRoute = true
			break
		}
	}

	if !hasStaticRoute {
		t.Error("Expected static file route to be configured")
	}
}
