# MealPal Environment Configuration
# Copy this file to .env and fill in your actual values

# Server Configuration
PORT=8080
ENVIRONMENT=development
SESSION_SECRET=your-super-secret-session-key-change-this-in-production

# Google OAuth Configuration
# Get these from Google Cloud Console: https://console.cloud.google.com/
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URL=http://localhost:8080/auth/google/callback

# Google Gemini API Configuration
# Get this from Google AI Studio: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your-gemini-api-key

# Production Configuration (when deploying)
# ENVIRONMENT=production
# GOOGLE_REDIRECT_URL=https://yourdomain.com/auth/google/callback
# SESSION_SECRET=a-very-long-random-string-for-production
